<script setup>

 const props = defineProps({
    item: Object,
    index: Number,
    lang: String
  })
 const emit = defineEmits(['update','help'])

</script>

<template>
  <div class="question">
    <label :for="item.name" class="label" :class="item.pour_campagne ? 'text-blue-500' : ''">{{ item.label[lang] }}</label>
    <div v-if="item.type === 'select'" :class="item.aide ? 'input-group': ''">
      <select 
        :name="item.name"
        :id="`input${index}`"
        class="form-select"
        :class="item.pour_campagne?'pour_campagne':''"
        @change="emit('update', $event, index, false, item.pour_campagne)"
        >
        <option value="">{{ $t('message.form_choice') }} {{ item.label[lang] }}</option>
        <option v-for="(option) in item.choix.filter(option => option.countrycode.includes(lang)).sort((a,b) => a.label.localeCompare(b.label))" :key="index" :value="option.label">{{ option.label }}</option>
      </select>
      <button v-if="item.aide" type="button" class="btn btn-outline-secondary" @click="emit('help', item.aide[lang])">{{ $t('message.form_help') }}</button>
    </div>
  </div>
</template>

<style scoped>
.question {
  border: 1px solid red;
  padding: 1rem;
  margin-bottom: 1rem;
}
</style>
