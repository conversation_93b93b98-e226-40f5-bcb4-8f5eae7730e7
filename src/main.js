import { createApp } from 'vue'
import { createI18n } from 'vue-i18n'


import App from './nomenclature.vue'
import './index.css'

const i18n = createI18n({
  locale: 'FR',
  messages: {
    FR: {
      message: {
        app_title: "Formulaires & Campagnes HubSpot",
        app_subtitle: "Utilitaire de composition de nom de formulaires et/ou de campagne HubSpot.",
        app_instructions: "Pour les campagnes, il faut saisir les champs <span class='pour_campagne'>en bleu</span>",
        app_loading: "Loading datas...",
        form_choice: "Choisir",
        form_complement: "Complement d'information obligatoire",
        form_validate: "Valider",
        form_entity: "Entité",
        form_pole: "Pôle",
        form_product: "Choix de la marque produit",
        form_action : "Choix de l'action",
        form_canal: "Choix du canal",
        form_help: "Aide",
        form_leadMaturation: "Maturation du lead généré",
        form_creationDate: "Date de création",
        form_formName: "Nom HubSpot du formulaire",
        form_formBtnCopy: "Copier le nom du formulaire",
        form_formCopyConfirmation: "Copié !",
        form_formBtnErase: "Effacer",
        form_campaignName: "Campagnes pouvant correspondre",
        form_campaignMessage: "Aucune campagne ne correspond aux critères sélectionnés",
        form_campaignInstruction: "Sélectionnez la campagne que vous voulez copier.",
        form_previousCampaign: "Remplace l'ancienne campagne :",
        form_campaignBtnCopy: "Copier le nom de la campagne",
        app_formDataVersion: "Version des données de formulaires",
        app_campaignDataVersion: "Version des données de campagnes",
        app_loadingError: "Erreur au chargement des données (Prévenir le pôle digital)",
        app_errorFormData: "Erreur de chargement des formulaires (Prévenir le pôle digital)",
        app_errorCampaignData: "Erreur de chargement des campagnes (Prévenir le pôle digital)"
      }
    },
    IT: {
      message: {
        app_title: "Moduli e campagne HubSpot",
        app_subtitle: "Utilità per la composizione dei nomi dei moduli e/o delle campagne HubSpot.",
        app_instructions: "Per le campagne, è necessario compilare i campi in blu",
        app_loading: "Caricamento dati in corso...",
        form_choic: "Scegli",
        form_complement: "Informazioni aggiuntive obbligatorie",
        form_validate: "Conferma",
        form_entity: "Entità",
        form_pole: "Polo",
        form_product: "Scelta del marchio del prodotto",
        form_action : "Scelta dell'azione",
        form_canal: "Scelta del canale",
        form_help: "Aiuto",
        form_leadMaturation: "Maturazione del lead generato",
        form_creationDate: "Data di creazione",
        form_formName: "Nome HubSpot del modulo",
        form_formBtnCopy: "Copia il nome del modulo",
        form_formBtnErase: "Cancella",
        form_formCopyConfirmation: "Copiato !",
        form_campaignName: "Campagne corrispondenti",
        form_campaignMessage: "Nessuna campagna corrisponde ai criteri selezionati",
        form_campaignInstruction: "Seleziona la campagna che desideri copiare.",
        form_previousCampaign: "Sostituisce la vecchia campagna:",
        form_campaignBtnCopy: "Copia il nome della campagna",
        app_formDataVersion: "Versione dei dati dei moduli",
        app_campaignDataVersion: "Versione dei dati delle campagne"
      }
    },
    RO: {
      message: {
        hello: 'Hello !'
      }
    }
  }
})

const app = createApp(App)

app.use(i18n)
app.mount('#app')
