<script setup>
 import {ref, onMounted} from "vue";
 import formInput from "./components/fromInput.vue";

 const dataLoaded = ref('loading')
 const beginning = ref(false)
 const check = ref(0)
 const forms = ref({})
 const campaigns = ref({})
 const config_forms = ref({})
 const config_campaigns = ref({})
 const campaign_name = ref([])
 const form_name = ref("")
 const lang = ref('FR')
 const errorMessages = ref([])

 // chargement des données
 function loadData() {
  fetch('./data_formulaires.json')
    .then(response => response.json())
    .then(data => {
      forms.value = data.questions
      config_forms.value = data.config
      dataLoaded.value = 'loaded'
    }).catch((error) => {
      dataLoaded.value = 'error'
      errorMessages.value.push("Erreur de chargement des formulaires")
    })
  fetch('./data_campagnes.json')
    .then(response => response.json())
    .then(data => {
      campaigns.value = data.campagnes
      config_campaigns.value = data.config
      dataLoaded.value = 'loaded'
    }).catch((error) => {
      dataLoaded.value = 'error'
      errorMessages.value.push("Erreur de chargement des campagnes")
    })
}

// Call loadData when component is mounted
onMounted(() => {
  loadData()
})
</script>

<template>
  <div class="container">
    <div class="hero mx-auto text-center">
      <h1 class="display-4 fw-bold">{{ $t('message.app_title') }}</h1>
      <p class="lead">{{ $t('message.app_subtitle') }}</p>
      <p class="fs-6 text-body-secondary" v-html="$t('message.app_instructions')"></p>
    </div>
    <div  class="row mt-5" v-if="dataLoaded === 'loading'">{{ $t('message.app_loading') }}</div>
    <div class="mx-auto row my-5 align-items-center">
      <div class="col-5 p-3 border border-secondary-subtle rounded-3 bg-opacity-25 bg-secondary" >
        <div class="row align-items-center">
          <div class="col" role="alert">
            Choose your country
          </div>
          <div class="col">
            <select name="lang" id="lang" v-model="lang" class="col form-select">
              <option value="FR">🇫🇷 France</option>
              <option value="IT">🇮🇹 Italy</option>
              <option value="RO">🇷🇴 Romania</option>
            </select>
          </div> <!-- col -->
        </div> <!-- row -->
      </div> <!-- col -->
    </div> <!-- row -->
  </div> <!-- container -->
  <div class="container">
    <div class="mx-auto max-w-7xl text-gray-700" id="zeform">
      <form class="w-full" @submit.prevent="">
        <div v-for="(item, index) in forms" :key="index">
          <formInput :item="item" :index="index" :lang="lang" @update="updateForm" />
        </div>
      </form>
    </div>
  </div> <!-- container -->
</template>

<style scoped></style>
