{"config": {"nom": "campagnes", "version": "03/07/25 18:04:06", "log": [{"date": "mercredi 2 juillet 2025  21h51", "auteur": "<EMAIL>", "commentaire": "mise à jour des noms de campagne"}, {"date": "jeudi 10 juillet 2025 12:00", "auteur": "<EMAIL>", "commentaire": "upload sur prod HubSpot"}, {"date": "mardi 16 septembre 2025 12:00", "auteur": "<EMAIL>", "commentaire": "version 0.1"}]}, "campagnes": [{"entite": "AGIRIS", "marque": "AUCUN", "pole": "PTPE & PCL", "type": "Générique", "nomCampagne": "AGIRIS - Contacts entrants", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "AUCUN", "pole": "PTPE & PCL", "type": "Générique", "nomCampagne": "AGIRIS - Evénements", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "AUCUN", "pole": "PTPE & PCL", "type": "Générique", "nomCampagne": "AGIRIS - Notoriété", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "AUCUN", "pole": "PTPE & PCL", "type": "Générique", "nomCampagne": "AGIRIS - Notoriété campagne payante", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "AUCUN", "pole": "PCL", "type": "Générique", "nomCampagne": "AGIRIS - Prestations PCL", "nomAncienneCampagne": "AGIRIS - Formations", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "AMICOMPTA", "pole": "PCL", "type": "Produit", "nomCampagne": "AGIRIS - AMICOMPTA PCL", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "AMICOMPTA", "pole": "PCL", "type": "Produit - ADS", "nomCampagne": "AGIRIS - AMICOMPTA PCL campagnes payantes", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "BF - Bilan Flash", "pole": "PCL", "type": "<PERSON><PERSON><PERSON>", "nomCampagne": "AGIRIS - Collaboration Expert-comptable & TPE - PCL", "nomAncienneCampagne": "AGIRIS - Accompagnement du dirigeant", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "bobbee by AGIRIS", "pole": "PCL", "type": "Produit", "nomCampagne": "AGIRIS - <PERSON><PERSON>", "nomAncienneCampagne": "AGIRIS - <PERSON><PERSON>", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "bobbee by AGIRIS", "pole": "PCL", "type": "Produit - ADS", "nomCampagne": "AGIRIS - Bobbee campagnes payantes", "nomAncienneCampagne": "AGIRIS - Bobbee campagnes payantes", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "COTTNS", "pole": "PCL", "type": "Produit - ADS", "nomCampagne": "AGIRIS - COTTNS PCL campagnes payantes", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "COTTNS", "pole": "PCL", "type": "Produit", "nomCampagne": "AGIRIS - COTTNS PCL", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "DR Web", "pole": "PCL", "type": "Produit - ADS", "nomCampagne": "AGIRIS - DR Web PCL campagnes payantes", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "DR Web", "pole": "PCL", "type": "Produit", "nomCampagne": "AGIRIS - DR Web PCL", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "Ecollaboratrice", "pole": "PCL", "type": "<PERSON><PERSON><PERSON>", "nomCampagne": "AGIRIS - Paie - RH - Social PCL", "nomAncienneCampagne": "AGIRIS - Paie - RH", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "EE - Evaluation Entreprise", "pole": "PCL", "type": "<PERSON><PERSON><PERSON>", "nomCampagne": "AGIRIS - Collaboration Expert-comptable & TPE PCL", "nomAncienneCampagne": "AGIRIS - Accompagnement du dirigeant", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "eFacture", "pole": "PCL", "type": "Produit - ADS", "nomCampagne": "AGIRIS - eFacture PCL campagnes payantes", "nomAncienneCampagne": "AGIRIS - Facture électronique campagnes payantes", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "eFacture", "pole": "PCL", "type": "Produit", "nomCampagne": "AGIRIS - eFacture PCL", "nomAncienneCampagne": "AGIRIS - Facture électronique", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "GP - Prévisions financières", "pole": "PCL", "type": "<PERSON><PERSON><PERSON>", "nomCampagne": "AGIRIS - Collaboration Expert-comptable & TPE PCL", "nomAncienneCampagne": "AGIRIS - Accompagnement du dirigeant", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "IO ECF", "pole": "PCL", "type": "<PERSON><PERSON><PERSON>", "nomCampagne": "AGIRIS - Production comptable PCL", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "ISACOMPTA", "pole": "PCL", "type": "Produit", "nomCampagne": "AGIRIS - ISACOMPTA PCL", "nomAncienneCampagne": "AGIRIS - ISACOMPTA", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "ISACOMPTA", "pole": "PCL", "type": "Produit - ADS", "nomCampagne": "AGIRIS - ISACOMPTA PCL Campagnes payantes", "nomAncienneCampagne": "AGIRIS - ISACOMPTA campagnes payantes", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "ISADECLARE", "pole": "PCL", "type": "Produit", "nomCampagne": "AGIRIS - ISACOMPTA PCL", "nomAncienneCampagne": "AGIRIS - ISACOMPTA", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "ISAGI CONNECT", "pole": "PCL", "type": "Produit", "nomCampagne": "AGIRIS - ISAGI PCL", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "ISAPAYE", "pole": "PCL", "type": "Produit", "nomCampagne": "AGIRIS - ISAPAYE PCL", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "ISAPAYE", "pole": "PCL", "type": "Produit - ADS", "nomCampagne": "AGIRIS - ISAPAYE PCL campagnes payantes", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "ISAREVISE", "pole": "PCL", "type": "Produit", "nomCampagne": "AGIRIS - ISACOMPTA PCL", "nomAncienneCampagne": "AGIRIS - ISACOMPTA", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "<PERSON><PERSON><PERSON> agricoles", "pole": "PCL", "type": "Produit", "nomCampagne": "AGIRIS - ISACOMPTA PCL", "nomAncienneCampagne": "AGIRIS - ISACOMPTA", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "Mon Coach", "pole": "PCL", "type": "Produit", "nomCampagne": "AGIRIS - Collaboration Expert-comptable & TPE PCL", "nomAncienneCampagne": "AGIRIS - ISACOMPTA", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "OR - Optimisation revenus", "pole": "PCL", "type": "<PERSON><PERSON><PERSON>", "nomCampagne": "AGIRIS - Collaboration Expert-comptable & TPE PCL", "nomAncienneCampagne": "AGIRIS - Accompagnement du dirigeant", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "Portail AGIRIS CONNECT", "pole": "PCL", "type": "Produit - ADS", "nomCampagne": "AGIRIS - Portail AGIRIS Connect PCL campagnes payantes", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "Portail AGIRIS CONNECT", "pole": "PCL", "type": "Produit", "nomCampagne": "AGIRIS - Portail AGIRIS Connect PCL", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "Prévisionnel IFC", "pole": "PCL", "type": "Produit - ADS", "nomCampagne": "AGIRIS - Prévisionnel IFC PCL campagnes payantes", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "Prévisionnel IFC", "pole": "PCL", "type": "Produit", "nomCampagne": "AGIRIS - Prévisionnel IFC PCL", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "Saas 360", "pole": "PCL", "type": "Produit", "nomCampagne": "AGIRIS - Saas 360 PCL", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "TVS'EXPERT", "pole": "PCL", "type": "<PERSON><PERSON><PERSON>", "nomCampagne": "AGIRIS - Production comptable PCL", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "AMICOMPTA", "pole": "PTPE", "type": "Produit - ADS", "nomCampagne": "AGIRIS - AMICOMPTA PTPE campagnes payantes", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "AMICOMPTA", "pole": "PTPE", "type": "Produit", "nomCampagne": "AGIRIS - AMICOMPTA PTPE", "nomAncienneCampagne": "AGIRIS - AMICOMPTA", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "BATAPPLI", "pole": "PTPE", "type": "Gamme - ADS", "nomCampagne": "AGIRIS - Animation des EC PTPE campagnes payantes", "nomAncienneCampagne": "AGIRIS - Animation partenaires comptables campagnes payantes", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "BATAPPLI", "pole": "PTPE", "type": "<PERSON><PERSON><PERSON>", "nomCampagne": "AGIRIS - Animation des EC PTPE", "nomAncienneCampagne": "AGIRIS - Animation partenaires comptables", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "bobbee business", "pole": "PTPE", "type": "Produit", "nomCampagne": "AGIRIS - Bobbee business", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "bobbee business", "pole": "PTPE", "type": "Produit - ADS", "nomCampagne": "AGIRIS - Bobbee business TPE campagnes payantes", "nomAncienneCampagne": "AGIRIS - Bobbee business TPE campagnes payantes", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "<PERSON><PERSON>", "pole": "PTPE", "type": "Gamme - ADS", "nomCampagne": "AGIRIS - Animation des EC PTPE campagnes payantes", "nomAncienneCampagne": "AGIRIS - Animation partenaires comptables campagnes payantes", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "<PERSON><PERSON>", "pole": "PTPE", "type": "<PERSON><PERSON><PERSON>", "nomCampagne": "AGIRIS - Animation des EC PTPE", "nomAncienneCampagne": "AGIRIS - Animation partenaires comptables", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "Ecollaboratrice", "pole": "PTPE", "type": "<PERSON><PERSON><PERSON>", "nomCampagne": "AGIRIS - ISAPAYE PTPE", "nomAncienneCampagne": "AGIRIS - ISAPAYE", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "eFacture", "pole": "PTPE", "type": "Produit - ADS", "nomCampagne": "AGIRIS - eFacture PTPE campagnes payantes", "nomAncienneCampagne": "AE - Facture électronique campagnes payantes", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "eFacture", "pole": "PTPE", "type": "Produit", "nomCampagne": "AGIRIS - eFacture PTPE", "nomAncienneCampagne": "AE - Facture électronique", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "ISACOMPTA", "pole": "PTPE", "type": "Produit", "nomCampagne": "AGIRIS - ISACOMPTA PTPE", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "ISACOMPTA", "pole": "PTPE", "type": "Produit - ADS", "nomCampagne": "AGIRIS - ISACOMPTA PTPE campagnes payantes", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "ISACOMPTA collaboratif", "pole": "PTPE", "type": "Gamme - ADS", "nomCampagne": "AGIRIS - Animation des EC PTPE campagnes payantes", "nomAncienneCampagne": "AGIRIS - Animation partenaires comptables campagnes payantes", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "ISACOMPTA collaboratif", "pole": "PTPE", "type": "<PERSON><PERSON><PERSON>", "nomCampagne": "AGIRIS - Animation des EC PTPE", "nomAncienneCampagne": "AGIRIS - Animation partenaires comptables", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "ISAFACT", "pole": "PTPE", "type": "Produit - ADS", "nomCampagne": "AGIRIS - ISAFACT PTPE campagnes payantes", "nomAncienneCampagne": "AE - ISAFACT Live campagnes payantes", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "ISAFACT", "pole": "PTPE", "type": "Produit", "nomCampagne": "AGIRIS - ISAFACT PTPE", "nomAncienneCampagne": "AE - ISAFACT Live", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "ISAPAYE", "pole": "PTPE", "type": "Produit - ADS", "nomCampagne": "AGIRIS - ISAPAYE PTPE campagnes payantes", "nomAncienneCampagne": "AGIRIS - ISAPAYE campagnes payantes", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "ISAPAYE", "pole": "PTPE", "type": "Produit", "nomCampagne": "AGIRIS - ISAPAYE PTPE", "nomAncienneCampagne": "AGIRIS - ISAPAYE", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "MENLOG", "pole": "PTPE", "type": "Gamme - ADS", "nomCampagne": "AGIRIS - Animation des EC PTPE campagnes payantes", "nomAncienneCampagne": "AGIRIS - Animation partenaires comptables campagnes payantes", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "MENLOG", "pole": "PTPE", "type": "<PERSON><PERSON><PERSON>", "nomCampagne": "AGIRIS - Animation des EC - PTPE", "nomAncienneCampagne": "AGIRIS - Animation partenaires comptables", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "Notes de frais", "pole": "PTPE", "type": "Gamme - ADS", "nomCampagne": "AGIRIS - Animation des EC PTPE campagnes payantes", "nomAncienneCampagne": "AGIRIS - Animation partenaires comptables campagnes payantes", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "Notes de frais", "pole": "PTPE", "type": "<PERSON><PERSON><PERSON>", "nomCampagne": "AGIRIS - Animation des EC PTPE", "nomAncienneCampagne": "AGIRIS - Animation partenaires comptables", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "Prévisionnel IFC", "pole": "PTPE", "type": "Produit", "nomCampagne": "AGIRIS - Prévisionnel IFC PTPE", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "Relevés bancaires", "pole": "PTPE", "type": "Gamme - ADS", "nomCampagne": "AGIRIS - Animation des EC PTPE campagnes payantes", "nomAncienneCampagne": "AGIRIS - Animation partenaires comptables campagnes payantes", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "Relevés bancaires", "pole": "PTPE", "type": "<PERSON><PERSON><PERSON>", "nomCampagne": "AGIRIS - Animation des EC PTPE", "nomAncienneCampagne": "AGIRIS - Animation partenaires comptables", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "ROBBIN", "pole": "PTPE", "type": "Gamme - ADS", "nomCampagne": "AGIRIS - Animation des EC PTPE campagnes payantes", "nomAncienneCampagne": "AGIRIS - Animation partenaires comptables campagnes payantes", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "ROBBIN", "pole": "PTPE", "type": "<PERSON><PERSON><PERSON>", "nomCampagne": "AGIRIS - Animation des EC PTPE", "nomAncienneCampagne": "AGIRIS - Animation partenaires comptables", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "Tableaux d'activité", "pole": "PTPE", "type": "Gamme - ADS", "nomCampagne": "AGIRIS - Animation des EC PTPE campagnes payantes", "nomAncienneCampagne": "AGIRIS - Animation partenaires comptables campagnes payantes", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "Tableaux d'activité", "pole": "PTPE", "type": "<PERSON><PERSON><PERSON>", "nomCampagne": "AGIRIS - Animation des EC PTPE", "nomAncienneCampagne": "AGIRIS - Animation partenaires comptables", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "TPVente", "pole": "PTPE", "type": "Gamme - ADS", "nomCampagne": "AGIRIS - Animation des EC PTPE campagnes payantes", "nomAncienneCampagne": "AGIRIS - Animation partenaires comptables campagnes payantes", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "TPVente", "pole": "PTPE", "type": "<PERSON><PERSON><PERSON>", "nomCampagne": "AGIRIS - Animation des EC PTPE", "nomAncienneCampagne": "AGIRIS - Animation partenaires comptables", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "WINO", "pole": "PTPE", "type": "Gamme - ADS", "nomCampagne": "AGIRIS - Animation des EC PTPE campagnes payantes", "nomAncienneCampagne": "AGIRIS - Animation partenaires comptables campagnes payantes", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "WINO", "pole": "PTPE", "type": "<PERSON><PERSON><PERSON>", "nomCampagne": "AGIRIS - Animation des EC PTPE", "nomAncienneCampagne": "AGIRIS - Animation partenaires comptables", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "Z de caisse", "pole": "PTPE", "type": "Gamme - ADS", "nomCampagne": "AGIRIS - Animation des EC PTPE campagnes payantes", "nomAncienneCampagne": "AGIRIS - Animation partenaires comptables campagnes payantes", "countrycode": "FR"}, {"entite": "AGIRIS", "marque": "Z de caisse", "pole": "PTPE", "type": "<PERSON><PERSON><PERSON>", "nomCampagne": "AGIRIS - Animation des EC PTPE", "nomAncienneCampagne": "AGIRIS - Animation partenaires comptables", "countrycode": "FR"}, {"entite": "II", "marque": "Autres produits II", "pole": "II", "type": "Générique", "nomCampagne": "II - Evenements campagne payantes", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "II", "marque": "Générique", "pole": "II", "type": "Générique", "nomCampagne": "II - Contacts entrants", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "CO-GC", "pole": "PVITI", "type": "Générique", "nomCampagne": "ISAGRI-CO-GC VITI", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "CO-GC", "pole": "PAGRI", "type": "Générique", "nomCampagne": "ISAGRI-CO-GC AGRI", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "Zoodiag", "pole": "PVITI", "type": "Produit", "nomCampagne": "ISAGRI - Zoodiag", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "TroupO", "pole": "PAGRI", "type": "Produit", "nomCampagne": "ISAGRI - Troup'O", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "<PERSON><PERSON> g<PERSON>", "pole": "PAGRI", "type": "Générique", "nomCampagne": "ISAGRI - Porc - Transversal", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "PigUP", "pole": "PAGRI", "type": "Générique", "nomCampagne": "ISAGRI - Pig'Up Hollandais", "nomAncienneCampagne": "", "countrycode": "BE-NL"}, {"entite": "ISAGRI FR", "marque": "TroupO Viande", "pole": "PAGRI", "type": "Produit", "nomCampagne": "ISAGRI  - TroupO Viande", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "TroupO Lait", "pole": "PAGRI", "type": "Produit", "nomCampagne": "ISAGRI  - TroupO Lait", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "PigUP", "pole": "PAGRI", "type": "Produit", "nomCampagne": "ISAGRI  - Pig'Up", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "Petits ruminants", "pole": "PAGRI", "type": "Générique", "nomCampagne": "ISAGRI  - Petits ruminants", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "ISAVIGNE", "pole": "PVITI", "type": "Produit", "nomCampagne": "ISAGRI  - ISAVIGNE", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "ISAETA", "pole": "PVITI", "type": "Produit", "nomCampagne": "ISAGRI  - IsaETA", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "ISACUVE", "pole": "PVITI", "type": "Produit", "nomCampagne": "ISAGRI  - ISACUVE", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "Gestion Commerciale AGRI", "pole": "PVITI", "type": "Produit", "nomCampagne": "ISAGRI  - Gestion Commerciale AGRI", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "Geofolia Viticulture", "pole": "PVITI", "type": "Produit", "nomCampagne": "ISAGRI  - Geofolia Viticulture", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "Geofolia ETA", "pole": "PAGRI", "type": "Produit", "nomCampagne": "ISAGRI  - Geofolia ETA", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "Générique", "pole": "MULTI", "type": "Générique", "nomCampagne": "ISAGRI  - Contacts entrants", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "ISALIM", "pole": "PAGRI", "type": "Produit", "nomCampagne": "ISAGRI-ISALIM", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "ISAGRI Technology", "pole": "PVITI", "type": "Générique", "nomCampagne": "ISAGRI Technology - Promotions campagnes payantes", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "ISAGRI Technology", "pole": "PVITI", "type": "Générique", "nomCampagne": "ISAGRI Technology - Inbound prospects campagnes payantes", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "ISAGRI Technology", "pole": "PVITI", "type": "Générique", "nomCampagne": "ISAGRI Technology - Inbound clients campagnes payantes", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "ISAGRI Technology", "pole": "PVITI", "type": "Générique", "nomCampagne": "ISAGRI Technology - Coopératives", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "ISAGRI Technology", "pole": "PVITI", "type": "Générique", "nomCampagne": "ISAGRI Technology  - Promotions", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "ISAGRI Technology", "pole": "PVITI", "type": "Générique", "nomCampagne": "ISAGRI Technology  - Infos produit", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "ISAGRI Technology", "pole": "PVITI", "type": "Générique", "nomCampagne": "ISAGRI Technology  - Inbound prospects", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "ISAGRI Technology", "pole": "PVITI", "type": "Générique", "nomCampagne": "ISAGRI Technology  - Inbound clients", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "ISAGRI Technology", "pole": "PVITI", "type": "Générique", "nomCampagne": "ISAGRI Technology  - Fidélisation", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI PT", "marque": "Multiproduits", "pole": "PINTER", "type": "Générique", "nomCampagne": "ISAGRI PT - CONTACTS ENTRANTS", "nomAncienneCampagne": "", "countrycode": "PT"}, {"entite": "ISAGRI IT", "marque": "Multiproduits", "pole": "PINTER", "type": "Générique", "nomCampagne": "ISAGRI IT - CONTACTS ENTRANTS", "nomAncienneCampagne": "", "countrycode": "IT"}, {"entite": "ISAGRI CH FR", "marque": "Viticulture", "pole": "PINTER", "type": "Générique", "nomCampagne": "ISAGRI CH FR - Viticulture", "nomAncienneCampagne": "", "countrycode": "CH-FR"}, {"entite": "ISAGRI CH FR", "marque": "TroupO", "pole": "PINTER", "type": "Produit", "nomCampagne": "ISAGRI CH FR - TroupO", "nomAncienneCampagne": "", "countrycode": "CH-FR"}, {"entite": "ISAGRI CH FR", "marque": "PigUP", "pole": "PINTER", "type": "Produit", "nomCampagne": "ISAGRI CH FR - PigUp", "nomAncienneCampagne": "", "countrycode": "CH-FR"}, {"entite": "ISAGRI CH FR", "marque": "<PERSON><PERSON><PERSON>", "pole": "PINTER", "type": "Produit", "nomCampagne": "ISAGRI CH FR - Isaovin", "nomAncienneCampagne": "", "countrycode": "CH-FR"}, {"entite": "ISAGRI CH DE", "marque": "<PERSON><PERSON><PERSON>", "pole": "PINTER", "type": "Produit", "nomCampagne": "ISAGRI CH DE - Isaovin", "nomAncienneCampagne": "", "countrycode": "CH-DE"}, {"entite": "ISAGRI CH FR", "marque": "Isacuve", "pole": "PINTER", "type": "Produit", "nomCampagne": "ISAGRI CH FR - Isacuve", "nomAncienneCampagne": "", "countrycode": "CH-FR"}, {"entite": "ISAGRI CH FR", "marque": "Geo<PERSON>", "pole": "PINTER", "type": "Produit", "nomCampagne": "ISAGRI CH FR - Geofolia", "nomAncienneCampagne": "", "countrycode": "CH-FR"}, {"entite": "ISAGRI CH DE", "marque": "Geo<PERSON>", "pole": "PINTER", "type": "Produit", "nomCampagne": "ISAGRI CH DE - Geofolia", "nomAncienneCampagne": "", "countrycode": "CH-DE"}, {"entite": "ISAGRI CH FR", "marque": "Multiproduits", "pole": "PINTER", "type": "Générique", "nomCampagne": "ISAGRI CH FR - GC", "nomAncienneCampagne": "", "countrycode": "CH-FR"}, {"entite": "ISAGRI CH FR", "marque": "Evènements", "pole": "PINTER", "type": "Générique", "nomCampagne": "ISAGRI CH FR - Evènements", "nomAncienneCampagne": "", "countrycode": "CH-FR"}, {"entite": "ISAGRI CH DE", "marque": "Evènements", "pole": "PINTER", "type": "Générique", "nomCampagne": "ISAGRI CH DE - Evènements", "nomAncienneCampagne": "", "countrycode": "CH-DE"}, {"entite": "ISAGRI CH DE", "marque": "Multiproduits", "pole": "PINTER", "type": "Générique", "nomCampagne": "ISAGRI CH DE - Contacts entrants", "nomAncienneCampagne": "", "countrycode": "CH-DE"}, {"entite": "ISAGRI CH", "marque": "Multiproduits", "pole": "PINTER", "type": "Générique", "nomCampagne": "ISAGRI CH FR - Contacts entrants", "nomAncienneCampagne": "", "countrycode": "CH-FR"}, {"entite": "ISAGRI BE NL", "marque": "PigUP", "pole": "PINTER", "type": "Produit", "nomCampagne": "ISAGRI BE NL - PigUp", "nomAncienneCampagne": "", "countrycode": "BE-NL"}, {"entite": "ISAGRI BE NL", "marque": "Gefolia", "pole": "PINTER", "type": "Produit", "nomCampagne": "ISAGRI BE NL - Geofolia", "nomAncienneCampagne": "", "countrycode": "BE-NL"}, {"entite": "ISAGRI BE NL", "marque": "Evènements", "pole": "PINTER", "type": "Générique", "nomCampagne": "ISAGRI BE NL - Evènements", "nomAncienneCampagne": "", "countrycode": "BE-NL"}, {"entite": "ISAGRI BE FR", "marque": "TroupO", "pole": "PINTER", "type": "Produit", "nomCampagne": "ISAGRI BE FR - TroupO", "nomAncienneCampagne": "", "countrycode": "BE-FR"}, {"entite": "ISAGRI BE FR", "marque": "PigUP", "pole": "PINTER", "type": "Produit", "nomCampagne": "ISAGRI BE FR - PigUp", "nomAncienneCampagne": "", "countrycode": "BE-FR"}, {"entite": "ISAGRI BE FR", "marque": "Petits ruminants", "pole": "PINTER", "type": "Produit", "nomCampagne": "ISAGRI BE FR - PigUp", "nomAncienneCampagne": "", "countrycode": "BE-FR"}, {"entite": "ISAGRI BE FR", "marque": "ISAGRI Technology", "pole": "PINTER", "type": "Produit", "nomCampagne": "ISAGRI BE FR - ISAGRI Technology", "nomAncienneCampagne": "", "countrycode": "BE-FR"}, {"entite": "ISAGRI BE FR", "marque": "Geo<PERSON>", "pole": "PINTER", "type": "Produit", "nomCampagne": "ISAGRI BE FR - Geofolia", "nomAncienneCampagne": "", "countrycode": "BE-FR"}, {"entite": "ISAGRI BE FR", "marque": "GC", "pole": "PINTER", "type": "Générique", "nomCampagne": "ISAGRI BE FR - GC", "nomAncienneCampagne": "", "countrycode": "BE-FR"}, {"entite": "ISAGRI BE FR", "marque": "Evènements", "pole": "PINTER", "type": "Générique", "nomCampagne": "ISAGRI BE FR - Evènements", "nomAncienneCampagne": "", "countrycode": "BE-FR"}, {"entite": "Landmark", "marque": "Evènements", "pole": "PINTER", "type": "Générique", "nomCampagne": "ISAGRI BE FR - Evènements", "nomAncienneCampagne": "", "countrycode": "BE-FR"}, {"entite": "ISAGRI FR", "marque": "ISAVIGNE", "pole": "PVITI", "type": "Produit", "nomCampagne": "ISAGRI - ISAVIGNE Live campagnes payantes", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "ISAVIGNE", "pole": "PVITI", "type": "Produit", "nomCampagne": "ISAGRI - ISAVIGNE campagnes payantes", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "ISAOVIN", "pole": "PAGRI", "type": "Produit", "nomCampagne": "ISAGRI - ISAOVIN", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "ISAFAF", "pole": "PAGRI", "type": "Produit", "nomCampagne": "ISAGRI - ISAFAF", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "ISAFACT Live", "pole": "PVITI", "type": "Produit", "nomCampagne": "ISAGRI - ISAFACT Live campagnes payantes", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "ISAFACT", "pole": "PVITI", "type": "Produit", "nomCampagne": "ISAGRI - ISAFACT campagnes payantes", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "ISAETA", "pole": "PVITI", "type": "Produit", "nomCampagne": "ISAGRI - IsaETA campagnes payantes", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "ISACUVE", "pole": "PVITI", "type": "Produit", "nomCampagne": "ISAGRI - ISACUVE campagnes payantes", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "ISACHEVRE", "pole": "PAGRI", "type": "Produit", "nomCampagne": "ISAGRI - ISACHEVRE", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "Geofolia Viticulture", "pole": "PAGRI", "type": "Produit", "nomCampagne": "ISAGRI - Geofolia Viticulture campagnes payantes", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "G<PERSON><PERSON>", "pole": "PAGRI", "type": "Produit", "nomCampagne": "ISAGRI - Geofolia OAD campagnes payantes", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "G<PERSON><PERSON>", "pole": "PAGRI", "type": "Produit", "nomCampagne": "ISAGRI - Geofolia <PERSON>", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "Geofolia ETA", "pole": "PAGRI", "type": "Produit", "nomCampagne": "ISAGRI - Geofolia ETA campagnes payantes", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "Geofolia <PERSON>", "pole": "PAGRI", "type": "Produit", "nomCampagne": "ISAGRI - Geofolia EasyTrack campagnes payantes", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "Geofolia <PERSON>", "pole": "PAGRI", "type": "Produit", "nomCampagne": "ISAGRI - Geofolia EasyTrack", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "<PERSON><PERSON>", "pole": "PTPE", "type": "Produit", "nomCampagne": "ISAGRI - <PERSON>bin", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "<PERSON><PERSON>", "pole": "PTPE", "type": "Produit", "nomCampagne": "ISAGRI - Robbin campagnes payantes", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "Pig'UP", "pole": "PAGRI", "type": "Produit", "nomCampagne": "ISAGRI - Pig'UP Anglais", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "ISAPAYE", "pole": "PTPE", "type": "Produit", "nomCampagne": "ISAGRI - ISAPAYE AGRI campagnes payantes", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "ISACOMPTA Live", "pole": "PTPE", "type": "Produit", "nomCampagne": "ISAGRI - ISACOMPTA migrations Live", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "ISACOMPTA", "pole": "PTPE", "type": "Produit", "nomCampagne": "ISAGRI - ISACOMPTA AGRI campagnes payantes", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "Geofolia Classique", "pole": "PAGRI", "type": "Produit", "nomCampagne": "ISAGRI - Geofolia Grandes Cultures campagnes payantes", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "Geofolia Classique", "pole": "PAGRI", "type": "Produit", "nomCampagne": "ISAGRI - Geofolia Grandes Cultures", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "eFacture", "pole": "PAGRI", "type": "Produit", "nomCampagne": "ISAGRI - Facture électronique", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "eFacture", "pole": "PAGRI", "type": "Produit", "nomCampagne": "ISAGRI - Facture électronique campagnes payantes", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "Multiproduits", "pole": "PAGRI", "type": "Produit", "nomCampagne": "ISAGRI - Bovins - Transversal", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "Multiproduits", "pole": "PAGRI", "type": "Produit", "nomCampagne": "ISAGRI - Agri - Transversal", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "ISAPAYE", "pole": "PTPE", "type": "Produit", "nomCampagne": "ISAGRI  - ISAPAYE AGRI", "nomAncienneCampagne": "", "countrycode": "FR"}, {"entite": "ISAGRI FR", "marque": "ISACOMPTA", "pole": "PTPE", "type": "Produit", "nomCampagne": "ISAGRI  - ISACOMPTA AGRI", "nomAncienneCampagne": "", "countrycode": "FR"}]}